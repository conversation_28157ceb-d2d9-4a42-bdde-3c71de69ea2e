import type React from 'react';
import { useState, useEffect } from 'react';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Home,
  MapPin,
  X,
  AlertCircle,
  ArrowLeft,
} from 'lucide-react';
import {
  useGetAddressesQuery,
  useCreateAddressMutation,
  useUpdateAddressMutation,
  useDeleteAddressMutation,
  type AddressFormData,
  type ValidationErrors,
  type Address,
} from '../store/api/apiSlice';
import { useNavigate } from 'react-router-dom';
import ResponsiveLoader from '@/components/Loader';
import { z } from 'zod';
import bg from '../../public/Images/bg4.png';
import LocationCard from '@/components/LocationCard';
import { showErrorToast, showSuccessToast } from '@/utils/toast';

const createAddressSchema = (
  existingAddresses: Address[],
  editingId?: number
) =>
  z
    .object({
      name: z
        .string()
        .min(1, 'Name is required')
        .min(2, 'Name must be at least 2 characters')
        .max(50, 'Name must be less than 50 characters')
        .regex(/^[a-zA-Z\s]+$/, 'Enter a valid Name'),
      address: z
        .string()
        .min(1, 'Address is required')
        .max(200, 'Address must be less than 200 characters'),
      icon: z.enum(['home', 'other'], {
        errorMap: () => ({ message: 'Please select a valid icon type' }),
      }),
    })
    .refine(
      data => {
        if (data.icon === 'home') {
          const otherAddresses = existingAddresses.filter(addr =>
            editingId ? addr.id !== editingId : true
          );

          const hasHomeAddress = otherAddresses.some(
            addr => addr.icon === 'home'
          );

          if (hasHomeAddress) {
            return false;
          }
        }
        return true;
      },
      {
        message: 'You can only have one home address.',
        path: ['icon'],
      }
    );

const Modal = ({
  isOpen,
  onClose,
  title,
  children,
}: {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}) => {
  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]'>
      <div className='bg-[#F2F2F2] rounded-lg p-6 w-full max-w-2xl mx-4 relative z-[10000] max-h-[90vh] overflow-y-auto'>
        <div className='flex justify-between items-center mb-4'>
          <h2 className='text-xl font-semibold text-gray-900'>{title}</h2>
          <button
            onClick={onClose}
            className='p-1 bg-gray-100 rounded-full transition-colors'
          >
            <X className='w-5 h-5' />
          </button>
        </div>
        {children}
      </div>
    </div>
  );
};

const AddressListContent: React.FC<{
  isLoading: boolean;
  addressesError: unknown;
  filteredAddresses: Address[];
  searchTerm: string;
  addresses: Address[];
  isDeleting: boolean;
  onEditAddress: (address: Address) => void;
  onDeleteClick: (id: number) => void;
  getIconComponent: (iconType: string) => React.ReactNode;
  ICON_COLOR: string;
}> = ({
  isLoading,
  addressesError,
  filteredAddresses,
  searchTerm,
  addresses,
  isDeleting,
  onEditAddress,
  onDeleteClick,
  getIconComponent,
  ICON_COLOR,
}) => {
  if (isLoading) {
    return (
      <div className=''>
        <ResponsiveLoader />
      </div>
    );
  }

  if (addressesError) {
    return (
      <div className='p-8 text-center'>
        <AlertCircle className='w-12 h-12 text-red-400 mx-auto mb-2' />
        <p className='text-red-500'>Failed to load addresses</p>
      </div>
    );
  }

  if (filteredAddresses.length === 0) {
    return (
      <div className='p-8 text-center'>
        <MapPin className='w-12 h-12 text-red-300 mx-auto mb-2' />
        <p className='text-gray-600'>
          {searchTerm
            ? 'No addresses found matching your search'
            : 'No addresses yet'}
        </p>
      </div>
    );
  }

  return (
    <div className='flex flex-col gap-6'>
      {filteredAddresses.map(address => (
        <div
          key={address.id}
          className='p-5 bg-[#F2F2F2] rounded-lg transition-colors flex-1'
        >
          <div className='flex items-start justify-between '>
            <div className='flex items-start space-x-4'>
              <div className={`p-3 rounded-full ${ICON_COLOR}`}>
                {getIconComponent(address.icon)}
              </div>
              <div className='flex-1'>
                <h3 className='text-lg font-medium text-gray-800'>
                  {address.name}
                </h3>
                {address.address && (
                  <p className='text-gray-800 mt-1 text-opacity-80'>
                    {address.address}
                  </p>
                )}
              </div>
            </div>
            <div className='flex items-center space-x-2'>
              <button
                onClick={() => onEditAddress(address)}
                className='p-2 text-gray-600 hover:text-nursery-blue hover:bg-blue-50 rounded-full transition-colors'
              >
                <Edit className='w-4 h-4' />
              </button>
              <button
                onClick={() => onDeleteClick(address.id)}
                disabled={isDeleting || addresses.length <= 1}
                className={`p-2 rounded-full transition-colors ${
                  addresses.length <= 1
                    ? 'text-gray-600 cursor-not-allowed'
                    : 'text-gray-600 hover:text-red-400 hover:bg-red-50'
                } disabled:opacity-50`}
                title={
                  addresses.length <= 1
                    ? 'At least one address is required'
                    : 'Delete address'
                }
              >
                <Trash2 className='w-4 h-4' />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

const IconSelector: React.FC<{
  selectedIcon: string;
  isHomeIconAvailable: boolean;
  onIconChange: (icon: string) => void;
  validationError?: string;
  getIconComponent: (iconType: string) => React.ReactNode;
  ICON_COLOR: string;
}> = ({
  selectedIcon,
  isHomeIconAvailable,
  onIconChange,
  validationError,
  getIconComponent,
  ICON_COLOR,
}) => (
  <div>
    <label className='block text-sm font-medium text-gray-700 mb-1'>
      Icon <span className='text-red-500'>*</span>
    </label>
    <div className='flex space-x-3'>
      {(['home', 'other'] as const).map(iconType => {
        const isDisabled = iconType === 'home' && !isHomeIconAvailable;
        return (
          <button
            key={iconType}
            type='button'
            onClick={() => !isDisabled && onIconChange(iconType)}
            disabled={isDisabled}
            className={`p-3 rounded-lg border-2 transition-all ${
              selectedIcon === iconType
                ? 'border-nursery-blue bg-blue-50'
                : isDisabled
                  ? 'border-gray-200 bg-gray-100 cursor-not-allowed opacity-50'
                  : 'border-gray-200 hover:border-gray-300'
            }`}
            title={isDisabled ? 'You already have a home address' : ''}
          >
            <div
              className={`${ICON_COLOR} p-2 rounded-full ${isDisabled ? 'opacity-50' : ''}`}
            >
              {getIconComponent(iconType)}
            </div>
            <span
              className={`block text-xs mt-1 capitalize ${
                isDisabled ? 'text-gray-400' : ''
              }`}
            >
              {iconType}
            </span>
          </button>
        );
      })}
    </div>
    {validationError && (
      <p className='mt-1 text-sm text-red-600 flex items-center'>
        <AlertCircle className='w-4 h-4 mr-1' />
        {validationError}
      </p>
    )}
  </div>
);

const handleApiError = (error: unknown, defaultMessage: string): string => {
  const apiError = error as { data?: { error?: string; message?: string } };
  return apiError?.data?.error || apiError?.data?.message || defaultMessage;
};

const AddressManager: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [formData, setFormData] = useState<AddressFormData>({
    name: '',
    address: '',
    icon: 'other',
    latitude: undefined,
    longitude: undefined,
  });
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>(
    {}
  );
  const [isLocationModalActive, setIsLocationModalActive] = useState(false);
  const [addressToDelete, setAddressToDelete] = useState<number | null>(null);

  const handleDeleteClick = (id: number) => {
    setAddressToDelete(id);
    setShowDeleteModal(true);
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setAddressToDelete(null);
  };

  const handleDeleteConfirm = async () => {
    if (addressToDelete === null) return;
    if (addresses.length <= 1) {
      showNotification(
        'error',
        'At least one address is required. You cannot delete your only address.'
      );
      setShowDeleteModal(false);
      setAddressToDelete(null);
      return;
    }
    try {
      await deleteAddress(addressToDelete).unwrap();
      showNotification('success', 'Address deleted successfully!');
    } catch (error: unknown) {
      console.error('Delete error:', error);
      showNotification(
        'error',
        handleApiError(error, 'Failed to delete address')
      );
    }
    setShowDeleteModal(false);
    setAddressToDelete(null);
  };

  const {
    data: addressesResponse,
    error: addressesError,
    isLoading,
  } = useGetAddressesQuery();
  const [createAddress, { isLoading: isCreating }] = useCreateAddressMutation();
  const [updateAddress, { isLoading: isUpdating }] = useUpdateAddressMutation();
  const [deleteAddress, { isLoading: isDeleting }] = useDeleteAddressMutation();
  const navigate = useNavigate();

  const addresses = addressesResponse?.data || [];

  const filteredAddresses = addresses.filter(
    address =>
      searchTerm === '' ||
      address.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      address.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  useEffect(() => {
    if (Object.keys(validationErrors).length > 0) {
      setValidationErrors({});
    }
  }, [formData, validationErrors]);

  const showNotification = (type: 'success' | 'error', message: string) => {
    if (type === 'success') {
      showSuccessToast(message);
    } else {
      showErrorToast(message);
    }
  };

  const validateForm = (): boolean => {
    try {
      const schema = createAddressSchema(addresses, editingAddress?.id);
      schema.parse(formData);
      setValidationErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: ValidationErrors = {};
        error.errors.forEach(err => {
          if (err.path[0]) {
            errors[err.path[0] as keyof ValidationErrors] = err.message;
          }
        });
        setValidationErrors(errors);
      }
      return false;
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      address: '',
      icon: 'other',
      latitude: undefined,
      longitude: undefined,
    });
    setValidationErrors({});
    setIsLocationModalActive(false);
  };

  const handleCreateAddress = async () => {
    if (!validateForm()) {
      showNotification('error', 'Please fix the validation errors');
      return;
    }

    try {
      await createAddress({
        name: formData.name.trim(),
        address: formData.address.trim() || '',
        icon: formData.icon,
        latitude: formData.latitude,
        longitude: formData.longitude,
      }).unwrap();
      showNotification('success', 'Address created successfully!');
      setIsCreateModalOpen(false);
      resetForm();
    } catch (error: unknown) {
      console.error('Create error:', error);
      showNotification(
        'error',
        handleApiError(error, 'Failed to create address')
      );
    }
  };

  const handleEditAddress = (address: Address) => {
    setEditingAddress(address);
    setFormData({
      name: address.name,
      address: address.address || '',
      icon: address.icon,
      latitude: address.latitude,
      longitude: address.longitude,
    });
    setValidationErrors({});
  };

  const handleUpdateAddress = async () => {
    if (!editingAddress) return;

    if (!validateForm()) {
      showNotification('error', 'Please fix the validation errors');
      return;
    }

    try {
      await updateAddress({
        id: editingAddress.id,
        data: {
          name: formData.name.trim(),
          address: formData.address.trim() || '',
          icon: formData.icon,
          latitude: formData.latitude,
          longitude: formData.longitude,
        },
      }).unwrap();

      showNotification('success', 'Address updated successfully!');
      setEditingAddress(null);
      resetForm();
    } catch (error: unknown) {
      console.error('Update error:', error);
      showNotification(
        'error',
        handleApiError(error, 'Failed to update address')
      );
    }
  };

  const getIconComponent = (iconType: string) => {
    switch (iconType) {
      case 'home':
        return <Home className='w-5 h-5' />;
      case 'other':
        return <MapPin className='w-5 h-5' />;
      default:
        return <Home className='w-5 h-5' />;
    }
  };

  const ICON_COLOR = 'text-white bg-nursery-blue';

  const closeModal = () => {
    if (editingAddress) {
      setEditingAddress(null);
    } else {
      setIsCreateModalOpen(false);
    }
    resetForm();
  };

  const handleInputChange = (field: keyof AddressFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const isHomeIconAvailable = () => {
    const otherAddresses = addresses.filter(addr =>
      editingAddress ? addr.id !== editingAddress.id : true
    );
    return !otherAddresses.some(addr => addr.icon === 'home');
  };

  const handleLocationSelect = (locationData: {
    latitude: number;
    longitude: number;
    address: string;
  }) => {
    setFormData(prev => ({
      ...prev,
      address: locationData.address,
      latitude: locationData.latitude,
      longitude: locationData.longitude,
    }));
    setIsLocationModalActive(false);
  };

  const renderFormField = (
    label: string,
    field: keyof AddressFormData,
    type: 'text' | 'textarea' | 'tel' | 'location' = 'text',
    placeholder = '',
    required = false
  ) => {
    if (type === 'location') {
      return (
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-1'>
            {label} {required && <span className='text-red-500'>*</span>}
          </label>
          <div className='relative'>
            <input
              type='text'
              value={formData.address}
              readOnly
              onClick={() => setIsLocationModalActive(true)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors cursor-pointer ${
                validationErrors.address
                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  : 'border-gray-300 focus:ring-nursery-blue focus:border-nursery-blue'
              }`}
              placeholder='Click to select location'
            />
            <button
              type='button'
              onClick={() => setIsLocationModalActive(true)}
              className='absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-nursery-blue'
            >
              <MapPin className='w-5 h-5' />
            </button>
          </div>
          {validationErrors.address && (
            <p className='mt-1 text-sm text-red-600 flex items-center'>
              <AlertCircle className='w-4 h-4 mr-1' />
              {validationErrors.address}
            </p>
          )}
        </div>
      );
    }

    return (
      <div>
        <label className='block text-sm font-medium text-gray-700 mb-1'>
          {label} {required && <span className='text-red-500'>*</span>}
        </label>
        {type === 'textarea' ? (
          <textarea
            value={formData[field] as string}
            onChange={e => handleInputChange(field, e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors ${
              validationErrors[field]
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : 'border-gray-300 focus:ring-nursery-blue focus:border-nursery-blue'
            }`}
            placeholder={placeholder}
            rows={3}
            autoComplete='off'
          />
        ) : (
          <input
            type={type}
            value={formData[field] as string}
            onChange={e => handleInputChange(field, e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors ${
              validationErrors[field]
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : 'border-gray-300 focus:ring-nursery-blue focus:border-nursery-blue'
            }`}
            placeholder={placeholder}
            required={required}
            autoComplete='off'
          />
        )}
        {validationErrors[field] && (
          <p className='mt-1 text-sm text-red-600 flex items-center'>
            <AlertCircle className='w-4 h-4 mr-1' />
            {validationErrors[field]}
          </p>
        )}
      </div>
    );
  };

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      {}
      <header className='relative w-full overflow-hidden text-white p-5 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
          <img
            src={bg}
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

        <div className='relative z-10 h-full min-w-full flex items-center md:mb-3 top-1 '>
          <button onClick={() => navigate(-1)} className='mr-3'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>Address Manager</h1>
        </div>
      </header>

      <div className='max-w-4xl mx-auto p-4'>
        {}
        <div className='bg-white rounded-lg p-6 mb-6'>
          <div className='flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4'>
            <div className='flex-1 relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5' />
              <input
                type='text'
                placeholder='Search addresses...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='w-full pl-10 pr-4 py-2 border bg-[#F2F2F2] border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-nursery-blue'
              />
            </div>
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className='bg-nursery-blue text-white px-4 py-2 rounded-md hover:bg-opacity-85 flex items-center justify-center space-x-2 transition-colors duration-200'
            >
              <Plus className='w-5 h-5' />
              <span>Add Address</span>
            </button>
          </div>
        </div>

        {/* Address List */}
        <div className='rounded-lg'>
          <AddressListContent
            isLoading={isLoading}
            addressesError={addressesError}
            filteredAddresses={filteredAddresses}
            searchTerm={searchTerm}
            addresses={addresses}
            isDeleting={isDeleting}
            onEditAddress={handleEditAddress}
            onDeleteClick={handleDeleteClick}
            getIconComponent={getIconComponent}
            ICON_COLOR={ICON_COLOR}
          />
        </div>
      </div>

      {}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={closeModal}
        title='Create New Address'
      >
        {isLocationModalActive ? (
          <div className='space-y-4'>
            <LocationCard
              onLocationSelect={handleLocationSelect}
              buttonText='Confirm Location'
              title='Select Address Location'
              showSearchHint={true}
            />
            <button
              onClick={() => setIsLocationModalActive(false)}
              className='w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200 mt-4'
            >
              Cancel
            </button>
          </div>
        ) : (
          <div className='space-y-4'>
            {renderFormField('Name', 'name', 'text', 'Enter full name', true)}
            {renderFormField('Address', 'address', 'location', '', true)}
            <IconSelector
              selectedIcon={formData.icon}
              isHomeIconAvailable={isHomeIconAvailable()}
              onIconChange={icon => handleInputChange('icon', icon)}
              validationError={validationErrors.icon}
              getIconComponent={getIconComponent}
              ICON_COLOR={ICON_COLOR}
            />

            <div className='flex space-x-3 pt-4'>
              <button
                onClick={handleCreateAddress}
                disabled={isCreating}
                className='flex-1 bg-nursery-blue text-white py-2 px-4 rounded-md hover:bg-opacity-85 hover:cursor-pointer flex items-center justify-center space-x-2 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed'
              >
                <span>{isCreating ? 'Saving...' : 'Save'}</span>
              </button>
              <button
                onClick={closeModal}
                className='flex-1 px-4 py-2 border bg-white border-gray-300 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200'
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </Modal>

      {}
      <Modal
        isOpen={!!editingAddress}
        onClose={closeModal}
        title='Edit Address'
      >
        {isLocationModalActive ? (
          <div className='space-y-4'>
            <LocationCard
              onLocationSelect={handleLocationSelect}
              buttonText='Confirm Location'
              title='Update Address Location'
              showSearchHint={true}
              initialLocation={
                formData.latitude && formData.longitude
                  ? {
                      lat: formData.latitude,
                      lng: formData.longitude,
                      address: formData.address,
                    }
                  : undefined
              }
            />
            <button
              onClick={() => setIsLocationModalActive(false)}
              className='w-full px-4 py-2 border bg-white border-gray-300 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200 mt-4'
            >
              Cancel
            </button>
          </div>
        ) : (
          <div className='space-y-4'>
            {renderFormField('Name', 'name', 'text', 'Enter name', true)}
            {renderFormField('Address', 'address', 'location', '', true)}
            <IconSelector
              selectedIcon={formData.icon}
              isHomeIconAvailable={isHomeIconAvailable()}
              onIconChange={icon => handleInputChange('icon', icon)}
              validationError={validationErrors.icon}
              getIconComponent={getIconComponent}
              ICON_COLOR={ICON_COLOR}
            />

            <div className='flex space-x-3 pt-4'>
              <button
                onClick={handleUpdateAddress}
                disabled={isUpdating}
                className='flex-1 bg-nursery-blue text-white py-2 px-4 rounded-md hover:bg-opacity-85 hover:cursor-pointer flex items-center justify-center space-x-2 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed'
              >
                <span>{isUpdating ? 'Saving...' : 'Save'}</span>
              </button>
              <button
                onClick={closeModal}
                className='flex-1 px-4 py-2 border bg-white border-gray-300 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200'
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </Modal>

      {}
      {showDeleteModal && (
        <div className='fixed inset-0 bg-black bg-opacity-55 flex items-center justify-center z-50 p-4'>
          <div className='bg-[#F2F2F2] rounded-xl p-6 w-full max-w-sm mx-4 shadow-xl'>
            <div className='flex justify-between items-center mb-4'>
              <h3 className='text-lg font-semibold text-gray-900'>
                Confirm Delete
              </h3>
              <button
                onClick={handleDeleteCancel}
                className='p-1 rounded-full transition-colors'
              >
                <X className='h-5 w-5 text-gray-500 hover:text-red-600 hover:scale-110 transition-transform duration-200' />
              </button>
            </div>

            <p className='text-gray-700 mb-6'>
              Are you sure you want to delete this address?
            </p>

            <div className='flex gap-3'>
              <button
                onClick={handleDeleteCancel}
                className='flex-1 px-4 py-2 border bg-white border-gray-300 text-gray-700 rounded-lg hover:bg-gray-300 hover:text-gray-800 transition-colors font-medium'
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirm}
                className='flex-1 px-4 py-2 bg-nursery-blue text-white rounded-lg hover:bg-nursery-darkBlue transition-colors duration-200 font-medium shadow-lg'
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddressManager;
