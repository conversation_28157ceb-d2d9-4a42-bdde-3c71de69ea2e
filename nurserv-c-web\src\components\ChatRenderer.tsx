import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Loader2,
  MessageSquare,
  X,
  Send,
  AlertCircle,
  ArrowLeft,
  Check,
  CheckCheck,
  Clock,
} from 'lucide-react';
import { ChatState } from './ChatInterface';

export interface ChatRendererProps {
  chatState: ChatState;
  mode: 'modal' | 'fullscreen';
  isOpen?: boolean;
}

export const ChatRenderer: React.FC<ChatRendererProps> = ({
  chatState,
  mode,
  isOpen = true,
}) => {
  const {
    newMessage,
    wsStatus,
    currentMessages,
    typingUsers,
    isLoading,
    isLoadingMessages: _isLoadingMessages,
    isSendingMessage,
    error,
    handleSendMessage,
    handleKeyPress,
    handleInputChange,
    handleClose,
    handleBack,
    safeFormatDate,
    messagesEndRef,
    userId,
    displayNurseName,
    nurseInfo,
  } = chatState;

  // Handle error state for fullscreen mode
  if (mode === 'fullscreen' && error) {
    return (
      <div className='min-h-screen flex flex-col items-center justify-center bg-white p-4'>
        <div className='bg-white rounded-lg p-6 max-w-md w-full shadow-md'>
          <div className='flex items-center justify-between mb-4'>
            <h3 className='text-lg font-semibold text-red-600'>Chat Error</h3>
          </div>
          <div className='flex items-center text-red-600 mb-4'>
            <AlertCircle className='h-5 w-5 mr-2' />
            <p className='text-gray-600'>
              {error || 'Failed to load chat. Please try again.'}
            </p>
          </div>
          <Button onClick={handleBack} className='w-full'>
            Back to Chats
          </Button>
        </div>
      </div>
    );
  }

  // Handle fullscreen loading state
  if (mode === 'fullscreen' && isLoading) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-white'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-nursery-blue mx-auto'></div>
          <p className='mt-4 text-gray-600'>Loading conversation...</p>
        </div>
      </div>
    );
  }

  // Don't render modal if not open
  if (mode === 'modal' && !isOpen) return null;

  // Render messages component
  const renderMessages = () => (
    <>
      {currentMessages.length === 0 ? (
        <div className='text-center text-gray-500 py-8'>
          <MessageSquare
            className={`${mode === 'fullscreen' ? 'h-12 w-12' : 'h-8 w-8'} mx-auto mb-${mode === 'fullscreen' ? '4' : '2'} opacity-50`}
          />
          <p className={mode === 'fullscreen' ? '' : 'text-sm'}>
            No messages yet. Start the conversation!
          </p>
        </div>
      ) : (
        currentMessages.map((message, idx) => (
          <div
            key={
              message.id ? `${message.id}-${message.timestamp}` : `msg-${idx}`
            }
            data-message-id={message.id || `msg-${idx}`}
            data-sender-id={message.senderId}
            className={`flex ${message.senderId === userId ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`${mode === 'fullscreen' ? 'max-w-xs lg:max-w-md px-4 py-2' : 'max-w-xs px-3 py-2'} rounded-lg ${
                message.senderId === userId
                  ? 'bg-nursery-blue text-white'
                  : 'bg-white border border-gray-200 text-gray-800'
              }`}
            >
              <p className='text-sm'>{message.content}</p>
              <p
                className={`text-xs mt-1 flex items-center ${message.senderId === userId ? 'text-white' : 'text-gray-500'}`}
                title={safeFormatDate(
                  message.timestamp,
                  'PPpp',
                  'Invalid date'
                )}
              >
                <span>{safeFormatDate(message.timestamp, 'h:mm a', '--')}</span>
                {message.senderId === userId && (
                  <span className='ml-1 flex items-center'>
                    {message.status === 'sending' && (
                      <Clock className='h-4 w-4 text-white' />
                    )}
                    {(message.status === 'sent' ||
                      message.status === 'delivered') && (
                      <Check className='h-4 w-4 text-white' />
                    )}
                    {message.status === 'read' && (
                      <CheckCheck className='h-4 w-4 text-white' />
                    )}
                  </span>
                )}
              </p>
            </div>
          </div>
        ))
      )}

      {/* Typing indicators */}
      {Object.values(typingUsers)
        .filter(
          (user: { userId: string; userName: string; timestamp: number }) =>
            user.userId !== userId
        )
        .map(
          (user: { userId: string; userName: string; timestamp: number }) => (
            <div key={user.userId} className='flex justify-start'>
              <div
                className={`bg-white border border-gray-200 text-gray-800 ${mode === 'fullscreen' ? 'px-4 py-2' : 'px-3 py-2'} rounded-lg`}
              >
                <div className='flex space-x-1'>
                  <div
                    className='h-2 w-2 bg-gray-400 rounded-full animate-bounce'
                    style={{ animationDelay: '0ms' }}
                  ></div>
                  <div
                    className='h-2 w-2 bg-gray-400 rounded-full animate-bounce'
                    style={{ animationDelay: '300ms' }}
                  ></div>
                  <div
                    className='h-2 w-2 bg-gray-400 rounded-full animate-bounce'
                    style={{ animationDelay: '600ms' }}
                  ></div>
                </div>
              </div>
            </div>
          )
        )}

      <div ref={messagesEndRef} />
    </>
  );

  // Render input area
  const renderInputArea = () => (
    <div
      className={`${mode === 'fullscreen' ? 'border-t p-4 bg-white sticky bottom-0' : 'border-t p-4 bg-white'}`}
    >
      <div className='flex space-x-2'>
        <Input
          value={newMessage}
          onChange={handleInputChange}
          onKeyDown={handleKeyPress}
          placeholder='Type your message...'
          disabled={isSendingMessage || !wsStatus.connected}
          className='flex-1'
          aria-label='Message input'
        />
        <Button
          onClick={handleSendMessage}
          disabled={
            !newMessage.trim() || isSendingMessage || !wsStatus.connected
          }
          className='bg-nursery-blue hover:bg-nursery-blue/90'
          aria-label='Send message'
        >
          {isSendingMessage ? (
            <Loader2 className='h-4 w-4 animate-spin' />
          ) : (
            <Send className='h-4 w-4' />
          )}
        </Button>
      </div>

      {/* Connection warning */}
      {!wsStatus.connected && wsStatus.error && (
        <p className='text-xs text-amber-600 mt-2 flex items-center'>
          <AlertCircle className='h-3 w-3 mr-1' />
          Connection issue. Messages may be delayed.
        </p>
      )}
    </div>
  );

  // Render fullscreen layout
  if (mode === 'fullscreen') {
    return (
      <div className='min-h-screen flex flex-col bg-white'>
        {/* Header */}
        <header className='bg-white border-b border-gray-200 px-4 py-3 flex items-center sticky top-0 z-10'>
          <button
            onClick={handleBack}
            className='mr-3 p-2 hover:bg-gray-100 rounded-full'
            aria-label='Back to chats'
          >
            <ArrowLeft className='h-5 w-5 text-gray-600' />
          </button>
          <div className='flex items-center'>
            <div className='w-10 h-10 bg-nursery-blue rounded-full flex items-center justify-center mr-3'>
              <span className='text-white text-sm font-semibold'>
                {displayNurseName.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <h1 className='text-lg font-semibold text-gray-900'>
                {displayNurseName}
              </h1>
              <p className='text-sm text-gray-500'>Nurse</p>
            </div>
          </div>

          {/* Connection status */}
          {!wsStatus.connected && (
            <div className='ml-auto flex items-center text-amber-600'>
              <AlertCircle className='h-4 w-4 mr-1' />
              <span className='text-xs'>Limited connectivity</span>
            </div>
          )}
        </header>

        {/* Messages area */}
        <div
          className='flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50'
          aria-live='polite'
          aria-relevant='additions'
        >
          {renderMessages()}
        </div>

        {/* Input area */}
        {renderInputArea()}
      </div>
    );
  }

  // Render modal layout
  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
      <div className='bg-white rounded-lg shadow-xl w-full max-w-md h-[600px] flex flex-col'>
        {/* Modal Header */}
        <div className='flex items-center justify-between p-4 border-b'>
          <div className='flex items-center'>
            <div className='w-10 h-10 bg-nursery-blue rounded-full flex items-center justify-center mr-3'>
              <span className='text-white text-sm font-semibold'>
                {nurseInfo?.nurse_given_name.charAt(0).toUpperCase() || 'N'}
              </span>
            </div>
            <div>
              <h3 className='text-lg font-semibold text-gray-900'>
                {nurseInfo?.nurse_given_name || 'Nurse'}
              </h3>
              <div className='flex items-center space-x-2'>
                <div
                  className={`h-2 w-2 rounded-full ${
                    wsStatus.connected
                      ? 'bg-green-500'
                      : wsStatus.connecting
                        ? 'bg-yellow-500'
                        : 'bg-red-500'
                  }`}
                />
                <p className='text-sm text-gray-500'>
                  {wsStatus.connected
                    ? 'Online'
                    : wsStatus.connecting
                      ? 'Connecting...'
                      : wsStatus.error
                        ? 'Connection Error'
                        : 'Offline'}
                </p>
              </div>
            </div>
          </div>
          <button
            onClick={handleClose}
            className='p-2 hover:bg-gray-100 rounded-full'
            aria-label='Close chat'
          >
            <X className='h-5 w-5 text-gray-600' />
          </button>
        </div>

        {/* Modal Messages */}
        <div className='flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50'>
          {isLoading ? (
            <div className='flex items-center justify-center h-full'>
              <div className='text-center'>
                <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-nursery-blue mx-auto'></div>
                <p className='mt-2 text-sm text-gray-600'>
                  Loading conversation...
                </p>
              </div>
            </div>
          ) : (
            renderMessages()
          )}
        </div>

        {/* Modal Input */}
        {renderInputArea()}
      </div>
    </div>
  );
};
