import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

const baseUrl =
  import.meta.env.VITE_API_BASE_URL ||
  'https://testnurservapi.nurserv.com/nurseApi';

interface Feedback {
  id: string;
  feedbackId: string;
  recipientName: string;
  name: string;
  rating: number;
  comments: string;
  submittedAt: string;
  createdAt: string;
  updatedAt: string;
}

interface GetFeedbackResponse {
  success?: boolean;
  feedback: Feedback[];
  rating_stats?: RatingStats;
  message?: string;
}

interface RatingStats {
  averageRating: number;
  totalFeedbacks: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

export const nurseApiSlice = createApi({
  reducerPath: 'nurseApi',
  baseQuery: fetchBaseQuery({
    baseUrl: baseUrl,
    prepareHeaders: (headers, { getState: _getState }) => {
      const token = localStorage.getItem('idToken');
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Feedback'],
  endpoints: builder => ({
    getFeedback: builder.query<GetFeedbackResponse, string>({
      query: nurseId => ({
        url: `profile/${nurseId}`,
        method: 'GET',
      }),
      providesTags: ['Feedback'],
      keepUnusedDataFor: 300,
      transformResponse: (response: {
        data?: GetFeedbackResponse;
        feedback?: Feedback[];
        rating_stats?: RatingStats;
        success?: boolean;
      }) => {
        if (response?.data) {
          return {
            feedback: response.data.feedback || [],
            rating_stats: response.data.rating_stats || null,
            success: response.success,
          };
        }

        return {
          feedback: response?.feedback || [],
          rating_stats: response?.rating_stats || null,
          success: response?.success !== false,
        };
      },
      transformErrorResponse: response => {
        console.error('Feedback API Error:', response);
        return response;
      },
    }),
  }),
});

export const { useGetFeedbackQuery } = nurseApiSlice;
